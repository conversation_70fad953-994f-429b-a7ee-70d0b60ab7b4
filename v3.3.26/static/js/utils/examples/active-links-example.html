<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exemplo - Sistema Universal de Classes Ativas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .nav a {
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 4px;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .nav a:hover {
            background-color: #e9ecef;
        }
        
        .nav a.active {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .drawer-nav {
            background: #343a40;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .drawer-nav a {
            display: block;
            color: #adb5bd;
            text-decoration: none;
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        
        .drawer-nav a:hover {
            background-color: #495057;
            color: white;
        }
        
        .drawer-nav a.active {
            background-color: #28a745;
            color: white;
        }
        
        .footer-nav {
            display: flex;
            justify-content: center;
            gap: 15px;
            padding: 20px;
            background: #6c757d;
            border-radius: 5px;
            margin-top: 30px;
        }
        
        .footer-nav a {
            color: white;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        
        .footer-nav a:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        .footer-nav a.active {
            background-color: #dc3545;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .demo-section h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .controls button {
            margin: 5px;
            padding: 8px 15px;
            border: 1px solid #007bff;
            background: white;
            color: #007bff;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .controls button:hover {
            background: #007bff;
            color: white;
        }
        
        .current-path {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sistema Universal de Classes Ativas</h1>
        <p>Este exemplo demonstra o funcionamento do script universal para gerenciar classes ativas em links de navegação.</p>
        
        <div class="current-path">
            <strong>Caminho atual:</strong> <span id="current-path">/</span>
        </div>
        
        <div class="demo-section">
            <h3>Navegação Principal (.n-link)</h3>
            <nav class="nav">
                <a href="/" class="n-link">Home</a>
                <a href="/about" class="n-link">Sobre</a>
                <a href="/services" class="n-link">Serviços</a>
                <a href="/contact" class="n-link">Contato</a>
                <a href="/blog" class="n-link">Blog</a>
            </nav>
        </div>
        
        <div class="demo-section">
            <h3>Navegação Lateral (.drawer-link)</h3>
            <nav class="drawer-nav">
                <a href="/" class="drawer-link">Dashboard</a>
                <a href="/profile" class="drawer-link">Perfil</a>
                <a href="/settings" class="drawer-link">Configurações</a>
                <a href="/help" class="drawer-link">Ajuda</a>
                <a href="/logout" class="drawer-link">Sair</a>
            </nav>
        </div>
        
        <div class="demo-section">
            <h3>Navegação do Rodapé (.f-link)</h3>
            <nav class="footer-nav">
                <a href="/privacy" class="f-link">Privacidade</a>
                <a href="/terms" class="f-link">Termos</a>
                <a href="/support" class="f-link">Suporte</a>
                <a href="/sitemap" class="f-link">Mapa do Site</a>
            </nav>
        </div>
        
        <div class="controls">
            <h3>Controles de Demonstração</h3>
            <p>Simule diferentes caminhos para ver o sistema em ação:</p>
            <button onclick="simulatePath('/')">Home (/)</button>
            <button onclick="simulatePath('/about')">Sobre (/about)</button>
            <button onclick="simulatePath('/services')">Serviços (/services)</button>
            <button onclick="simulatePath('/contact')">Contato (/contact)</button>
            <button onclick="simulatePath('/profile')">Perfil (/profile)</button>
            <button onclick="simulatePath('/settings')">Configurações (/settings)</button>
            <button onclick="simulatePath('/privacy')">Privacidade (/privacy)</button>
            <button onclick="simulatePath('/terms')">Termos (/terms)</button>
            <br>
            <button onclick="clearAllStates()">Limpar Todos os Estados</button>
            <button onclick="runAdvancedExample()">Exemplo Avançado</button>
            <button onclick="runHashExample()">Exemplo com Hash</button>
        </div>
        
        <div class="demo-section">
            <h3>Log de Atividades</h3>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">Limpar Log</button>
        </div>
    </div>

    <!-- Incluir o script universal -->
    <script src="../active-links.js"></script>
    
    <script>
        // Função para adicionar logs
        function addLog(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // Função para limpar o log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Função para simular mudança de caminho
        function simulatePath(path) {
            // Simula mudança de URL (apenas para demonstração)
            history.pushState({}, '', path);
            
            // Atualiza o display do caminho atual
            document.getElementById('current-path').textContent = path;
            
            // Executa o sistema de classes ativas
            initializeActiveLinks();
            
            addLog(`Navegou para: ${path}`);
        }
        
        // Função para limpar todos os estados
        function clearAllStates() {
            clearAllActiveStates();
            addLog('Todos os estados ativos foram limpos');
        }
        
        // Exemplo avançado
        function runAdvancedExample() {
            const currentPath = window.location.pathname;
            
            initializeActiveLinksAdvanced({
                linkSelectors: ['.n-link', '.drawer-link', '.f-link'],
                activeClass: 'active',
                matchAttribute: 'href',
                exactMatch: false, // Correspondência parcial
                enableAria: true,
                enableLogging: true,
                customMatcher: (link, path) => {
                    // Lógica personalizada: links que começam com /admin sempre ativos em /admin/*
                    const href = link.getAttribute('href');
                    if (href && href.startsWith('/admin') && path.startsWith('/admin')) {
                        return true;
                    }
                    return href === path;
                }
            });
            
            addLog('Executou exemplo avançado com correspondência parcial');
        }
        
        // Exemplo com hash
        function runHashExample() {
            // Simula navegação por hash
            window.location.hash = '#home';
            
            // Cria alguns links com data-page-target para demonstração
            const tempNav = document.createElement('nav');
            tempNav.innerHTML = `
                <a href="#home" data-page-target="home">Home</a>
                <a href="#about" data-page-target="about">About</a>
                <a href="#contact" data-page-target="contact">Contact</a>
            `;
            
            initializeActiveLinksHash();
            addLog('Executou exemplo com navegação por hash');
        }
        
        // Interceptar cliques nos links para demonstração
        document.addEventListener('click', function(e) {
            if (e.target.matches('a[href]')) {
                e.preventDefault();
                const href = e.target.getAttribute('href');
                simulatePath(href);
            }
        });
        
        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            // Atualiza o display do caminho atual
            document.getElementById('current-path').textContent = window.location.pathname;
            
            // Log inicial
            addLog('Sistema de classes ativas inicializado');
            addLog(`Caminho inicial: ${window.location.pathname}`);
            
            // Intercepta logs do console para mostrar na interface
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                if (args[0] && args[0].includes('[ActiveLinks]')) {
                    addLog(args.join(' '));
                }
            };
        });
        
        // Escuta mudanças de histórico
        window.addEventListener('popstate', function() {
            document.getElementById('current-path').textContent = window.location.pathname;
            addLog(`Navegação por histórico: ${window.location.pathname}`);
        });
    </script>
</body>
</html>
