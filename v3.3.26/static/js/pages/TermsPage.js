/**
 * TermsPage Module
 * Handles the terms of service page content
 */

class TermsPage {
  constructor() {
    this.pageId = 'terms';
    this.initialized = false;
  }

  /**
   * Get the HTML content for the terms page
   */
  getContent() {
    return `
      <section class="tos-section">
        <div class="tos-container">
          <div class="header-content">
            <h1 data-translate="tos"></h1>
            <p class="description" data-translate="tosDesc"></p>
            <p class="date" data-translate="ToSdate"></p>
          </div>

          <div class="cards-grid">
            <div class="tos-card">
              <div class="card-header">
                <i class="fas fa-user-shield"></i>
                <h2 data-translate="tosAU"></h2>
              </div>
              <p data-translate="tosAUdesc"></p>
            </div>

            <div class="tos-card">
              <div class="card-header">
                <i class="fas fa-copyright"></i>
                <h2 data-translate="tosCO"></h2>
              </div>
              <p data-translate="tosCOdesc"></p>
            </div>

            <div class="tos-card">
              <div class="card-header">
                <i class="fas fa-gavel"></i>
                <h2 data-translate="tosEL"></h2>
              </div>
              <p data-translate="tosELdesc"></p>
            </div>

            <div class="tos-card">
              <div class="card-header">
                <i class="fas fa-exclamation-circle"></i>
                <h2 data-translate="tosLoL"></h2>
              </div>
              <p data-translate="tosLoLdesc"></p>
            </div>

            <div class="tos-card">
              <div class="card-header">
                <i class="fas fa-tasks"></i>
                <h2 data-translate="tosCttT"></h2>
              </div>
              <p data-translate="tosCttTdesc"></p>
            </div>

            <div class="tos-card">
              <div class="card-header">
                <i class="fas fa-envelope"></i>
                <h2 data-translate="tosCONTACT"></h2>
              </div>
              <p data-translate="tosCONTACTdesc"></p>
            </div>
          </div>
        </div>
      </section>
    `;
  }

  /**
   * Initialize the terms page functionality
   */
  init() {
    if (this.initialized) return;

    // Initialize any terms page specific functionality
    this.initCardAnimations();

    this.initialized = true;
  }

  /**
   * Initialize card animations
   */
  initCardAnimations() {
    const cards = document.querySelectorAll('.tos-card');
    
    // Add staggered animation to cards
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
          }, index * 100);
        }
      });
    });

    cards.forEach(card => {
      observer.observe(card);
    });
  }

  /**
   * Clean up when leaving the page
   */
  destroy() {
    // Clean up any event listeners or intervals if needed
    this.initialized = false;
  }
}

// Export for use in other modules
window.TermsPage = TermsPage;
